// src/styles/theme.ts
'use client';

import { createTheme } from '@mui/material/styles';

const theme = createTheme({
  components: {
    // 针对所有 TextField 组件的全局样式覆盖
    MuiTextField: {
      styleOverrides: {
        root: {
          // 标签颜色
          '& .MuiInputLabel-root': {
            color: 'var(--secondary-text)',
          },
          // 输入框的外框
          '& .MuiOutlinedInput-root': {
            // 默认状态下的边框颜色
            '& fieldset': {
              borderColor: 'var(--input-border)',
            },
            // 鼠标悬停时的边框颜色
            '&:hover fieldset': {
              borderColor: 'var(--link-color)',
            },
            // 输入的文字颜色
            '& .MuiOutlinedInput-input': {
              color: 'var(--primary-text)',
            },
          },
        },
      },
    },
  },
});

export default theme;
